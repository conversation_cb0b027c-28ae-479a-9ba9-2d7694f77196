# -*- coding: utf-8 -*-
"""
简单的PLC结构发现测试
用于验证发现功能是否正常工作
"""

import sys
from s7_client import S7Client
from config import app_config


def test_discovery_basic():
    """基本发现功能测试"""
    print("=" * 50)
    print("PLC结构发现基本测试")
    print("=" * 50)
    
    client = S7Client()
    
    # 获取配置
    plc_config = app_config.get_plc_config()
    ip = plc_config.get("ip", "************")
    rack = plc_config.get("rack", 0)
    slot = plc_config.get("slot", 1)
    
    print(f"尝试连接到PLC: {ip}:{rack}:{slot}")
    
    # 连接PLC
    success = client.connect_plc(ip, rack, slot)
    
    if not success:
        print("✗ 无法连接到PLC")
        print("可能的原因:")
        print("1. PLC未开机或网络不通")
        print("2. IP地址、Rack、Slot参数错误")
        print("3. 防火墙阻止连接")
        print("4. PLC不允许外部连接")
        return False
    
    print("✓ PLC连接成功")
    
    try:
        # 测试单个DB扫描
        print("\n" + "-" * 30)
        print("测试单个数据块扫描")
        print("-" * 30)
        
        db_info = client.scan_data_blocks(1, 5, 256)  # 扫描DB1-DB5，最大256字节
        
        accessible_count = sum(1 for info in db_info.values() if info["accessible"])
        print(f"扫描DB1-DB5，发现 {accessible_count} 个可访问的数据块")
        
        for db_num, info in db_info.items():
            if info["accessible"]:
                print(f"  ✓ DB{db_num}: {info['size']} 字节")
            else:
                print(f"  ✗ DB{db_num}: 不可访问")
        
        # 如果有可访问的DB，测试变量分析
        accessible_dbs = [db for db, info in db_info.items() if info["accessible"]]
        
        if accessible_dbs:
            test_db = accessible_dbs[0]
            test_data = db_info[test_db]["data"]
            
            print(f"\n测试DB{test_db}的变量分析:")
            variables = client.analyze_db_data(test_db, test_data)
            
            if variables:
                print(f"发现 {len(variables)} 个可能的变量:")
                for i, var in enumerate(variables[:5]):  # 只显示前5个
                    print(f"  {i+1}. {var['name']}: {var['type']} = {var['value']}")
                if len(variables) > 5:
                    print(f"  ... 还有 {len(variables) - 5} 个变量")
            else:
                print("  未发现明显的变量模式")
        
        # 测试完整发现（小范围）
        print("\n" + "-" * 30)
        print("测试完整结构发现")
        print("-" * 30)
        
        result = client.discover_plc_structure(1, 3)  # 只发现DB1-DB3
        
        summary = result.get("summary", {})
        print(f"发现摘要:")
        print(f"  扫描数据块: {summary.get('total_dbs_scanned', 0)}")
        print(f"  可访问数据块: {summary.get('accessible_dbs', 0)}")
        print(f"  发现变量: {summary.get('total_variables_found', 0)}")
        print(f"  变量类型: {summary.get('variable_types', {})}")
        
        if summary.get('total_variables_found', 0) > 0:
            print("✓ 发现功能正常工作")
            return True
        else:
            print("⚠ 发现功能工作，但未找到变量（可能PLC中没有数据）")
            return True
            
    except Exception as e:
        print(f"✗ 发现过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        client.disconnect_plc()
        print("\n✓ PLC连接已断开")


def test_discovery_methods():
    """测试各个发现方法"""
    print("\n" + "=" * 50)
    print("测试各个发现方法")
    print("=" * 50)
    
    client = S7Client()
    
    # 测试不连接PLC的情况
    print("1. 测试未连接状态:")
    db_info = client.scan_data_blocks(1, 2)
    print(f"   未连接时扫描结果: {len(db_info)} 个结果")
    
    result = client.discover_plc_structure(1, 2)
    print(f"   未连接时发现结果: {len(result)} 个键")
    
    # 测试数据分析方法
    print("\n2. 测试数据分析方法:")
    
    # 创建一些测试数据
    test_data = bytearray([
        0x01,  # BOOL
        0x00, 0x64,  # INT = 100
        0x00, 0x00, 0x03, 0xE8,  # DINT = 1000
        0x42, 0xC8, 0x00, 0x00,  # REAL = 100.0
    ])
    
    variables = client.analyze_db_data(999, test_data)
    print(f"   分析测试数据，发现 {len(variables)} 个变量")
    
    for var in variables[:3]:  # 显示前3个
        print(f"     {var['name']}: {var['type']} = {var['value']}")
    
    print("✓ 方法测试完成")


def main():
    """主函数"""
    print("PLC结构发现功能测试")
    
    # 检查依赖
    try:
        import snap7
        print("✓ snap7库已安装")
    except ImportError:
        print("✗ 缺少snap7库，请运行: pip install python-snap7")
        return 1
    
    # 测试发现方法
    test_discovery_methods()
    
    # 测试基本发现功能
    success = test_discovery_basic()
    
    if success:
        print("\n" + "=" * 50)
        print("✓ 测试完成！发现功能正常工作")
        print("现在可以在主程序中使用PLC结构发现功能")
        print("=" * 50)
        return 0
    else:
        print("\n" + "=" * 50)
        print("✗ 测试失败，请检查PLC连接")
        print("=" * 50)
        return 1


if __name__ == "__main__":
    sys.exit(main())
