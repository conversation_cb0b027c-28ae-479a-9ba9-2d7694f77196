# -*- coding: utf-8 -*-
"""
PLC结构发现测试脚本
用于测试PLC数据块扫描和变量发现功能
"""

import sys
import json
import time
from s7_client import S7Client
from config import app_config


def test_plc_discovery():
    """测试PLC结构发现功能"""
    print("=" * 60)
    print("PLC结构发现测试")
    print("=" * 60)
    
    client = S7Client()
    
    # 获取配置
    plc_config = app_config.get_plc_config()
    ip = plc_config.get("ip", "************")
    rack = plc_config.get("rack", 0)
    slot = plc_config.get("slot", 1)
    
    print(f"连接到PLC: {ip}:{rack}:{slot}")
    
    # 连接PLC
    success = client.connect_plc(ip, rack, slot)
    
    if not success:
        print("✗ 无法连接到PLC，请检查连接参数")
        return False
    
    print("✓ PLC连接成功")
    
    try:
        # 测试数据块扫描
        print("\n" + "-" * 40)
        print("测试数据块扫描")
        print("-" * 40)
        
        db_info = client.scan_data_blocks(1, 20, 512)  # 扫描DB1-DB20，最大512字节
        
        accessible_dbs = [db for db, info in db_info.items() if info["accessible"]]
        print(f"发现 {len(accessible_dbs)} 个可访问的数据块: {accessible_dbs}")
        
        for db_num in accessible_dbs[:5]:  # 只显示前5个
            info = db_info[db_num]
            print(f"  DB{db_num}: 大小={info['size']}字节")
        
        # 测试完整结构发现
        print("\n" + "-" * 40)
        print("测试完整结构发现")
        print("-" * 40)
        
        result = client.discover_plc_structure(1, 10)  # 发现DB1-DB10
        
        # 显示摘要
        summary = result.get("summary", {})
        print(f"扫描摘要:")
        print(f"  总扫描数据块: {summary.get('total_dbs_scanned', 0)}")
        print(f"  可访问数据块: {summary.get('accessible_dbs', 0)}")
        print(f"  总数据大小: {summary.get('total_data_size', 0)} 字节")
        print(f"  发现变量数: {summary.get('total_variables_found', 0)}")
        print(f"  变量类型分布: {summary.get('variable_types', {})}")
        
        # 显示部分变量
        variables = result.get("variables", [])
        if variables:
            print(f"\n发现的变量 (显示前10个):")
            print(f"{'变量名':<25} {'DB':<4} {'偏移':<6} {'类型':<8} {'值':<15} {'描述'}")
            print("-" * 80)
            
            for var in variables[:10]:
                name = var.get("name", "")[:24]
                db = var.get("db", "")
                offset = var.get("offset", "")
                var_type = var.get("type", "")
                value = str(var.get("value", ""))[:14]
                desc = var.get("description", "")[:20]
                
                print(f"{name:<25} {db:<4} {offset:<6} {var_type:<8} {value:<15} {desc}")
        
        # 保存发现结果
        print("\n" + "-" * 40)
        print("保存发现结果")
        print("-" * 40)
        
        output_file = "plc_discovery_result.json"
        
        # 准备导出数据
        export_data = {
            "discovery_time": time.strftime("%Y-%m-%d %H:%M:%S"),
            "plc_info": {
                "ip": ip,
                "rack": rack,
                "slot": slot
            },
            "summary": summary,
            "data_blocks": {
                str(k): {
                    "size": v.get("size", 0),
                    "accessible": v.get("accessible", False)
                }
                for k, v in result.get("data_blocks", {}).items()
                if v.get("accessible", False)
            },
            "variables": variables
        }
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(export_data, f, indent=2, ensure_ascii=False)
        
        print(f"✓ 发现结果已保存到: {output_file}")
        
        # 生成配置文件
        config_file = "discovered_variables_config.json"
        config_variables = {}
        
        for var in variables:
            var_name = var.get("name", "")
            if var_name and var.get("type") in ["BOOL", "INT", "DINT", "REAL"]:  # 只导出常用类型
                config_var = {
                    "db": var.get("db"),
                    "offset": var.get("offset"),
                    "type": var.get("type"),
                    "description": var.get("description", "")
                }
                
                if var.get("type") == "BOOL" and "bit" in var:
                    config_var["bit"] = var["bit"]
                
                config_variables[var_name] = config_var
        
        config_data = {
            "plc": plc_config,
            "variables": config_variables
        }
        
        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(config_data, f, indent=2, ensure_ascii=False)
        
        print(f"✓ 变量配置已保存到: {config_file}")
        print(f"  可用变量数: {len(config_variables)}")
        
        return True
        
    except Exception as e:
        print(f"✗ 发现过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        client.disconnect_plc()
        print("\n✓ PLC连接已断开")


def analyze_discovery_result():
    """分析已保存的发现结果"""
    print("\n" + "=" * 60)
    print("分析发现结果")
    print("=" * 60)
    
    try:
        with open("plc_discovery_result.json", 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        print(f"发现时间: {data.get('discovery_time', 'Unknown')}")
        print(f"PLC信息: {data.get('plc_info', {})}")
        
        summary = data.get("summary", {})
        print(f"\n摘要信息:")
        for key, value in summary.items():
            print(f"  {key}: {value}")
        
        # 按类型统计变量
        variables = data.get("variables", [])
        type_stats = {}
        db_stats = {}
        
        for var in variables:
            var_type = var.get("type", "Unknown")
            db_num = var.get("db", "Unknown")
            
            type_stats[var_type] = type_stats.get(var_type, 0) + 1
            db_stats[db_num] = db_stats.get(db_num, 0) + 1
        
        print(f"\n变量类型统计:")
        for var_type, count in sorted(type_stats.items()):
            print(f"  {var_type}: {count}")
        
        print(f"\n数据块分布:")
        for db_num, count in sorted(db_stats.items()):
            print(f"  DB{db_num}: {count} 个变量")
        
        # 显示一些有趣的变量
        print(f"\n有趣的变量示例:")
        
        # REAL类型的变量（可能是传感器数据）
        real_vars = [v for v in variables if v.get("type") == "REAL"]
        if real_vars:
            print(f"  浮点数变量 (前5个):")
            for var in real_vars[:5]:
                print(f"    {var.get('name', '')}: {var.get('value', '')}")
        
        # BOOL类型的变量（可能是状态位）
        bool_vars = [v for v in variables if v.get("type") == "BOOL"]
        if bool_vars:
            print(f"  布尔变量 (前5个):")
            for var in bool_vars[:5]:
                print(f"    {var.get('name', '')}: {var.get('value', '')}")
        
        return True
        
    except FileNotFoundError:
        print("✗ 未找到发现结果文件，请先运行发现测试")
        return False
    except Exception as e:
        print(f"✗ 分析发现结果失败: {e}")
        return False


def main():
    """主函数"""
    print("PLC结构发现测试工具")
    print("=" * 60)
    
    # 检查依赖
    try:
        import snap7
        import PySide6
    except ImportError as e:
        print(f"✗ 缺少依赖库: {e}")
        print("请运行: pip install -r requirements.txt")
        return 1
    
    # 运行测试
    success = test_plc_discovery()
    
    if success:
        # 分析结果
        analyze_discovery_result()
        
        print("\n" + "=" * 60)
        print("测试完成!")
        print("生成的文件:")
        print("  - plc_discovery_result.json: 完整发现结果")
        print("  - discovered_variables_config.json: 可用的变量配置")
        print("\n使用建议:")
        print("1. 查看 plc_discovery_result.json 了解PLC结构")
        print("2. 将 discovered_variables_config.json 中的变量复制到主配置文件")
        print("3. 根据实际需要调整变量名称和描述")
        print("4. 在主程序中使用这些变量进行监控和控制")
        
        return 0
    else:
        print("\n✗ 测试失败")
        return 1


if __name__ == "__main__":
    sys.exit(main())
