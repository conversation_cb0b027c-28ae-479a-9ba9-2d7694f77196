# -*- coding: utf-8 -*-
"""
调试PLC结构发现功能
"""

import sys
from s7_client import S7Client
from config import app_config


def debug_connection():
    """调试PLC连接"""
    print("=" * 50)
    print("调试PLC连接")
    print("=" * 50)
    
    client = S7Client()
    
    # 获取配置
    plc_config = app_config.get_plc_config()
    ip = plc_config.get("ip", "************")
    rack = plc_config.get("rack", 0)
    slot = plc_config.get("slot", 1)
    
    print(f"PLC配置: IP={ip}, Rack={rack}, Slot={slot}")
    
    # 尝试连接
    print("尝试连接PLC...")
    success = client.connect_plc(ip, rack, slot)
    
    if success:
        print("✓ PLC连接成功")
        
        # 检查连接状态
        is_connected = client.is_connected()
        print(f"连接状态检查: {is_connected}")
        
        # 尝试读取一个简单的数据块
        print("\n测试数据读取:")
        try:
            # 尝试读取DB1的前4个字节
            data = client.read_db(1, 0, 4)
            if data is not None:
                print(f"✓ 成功读取DB1: {[hex(b) for b in data]}")
            else:
                print("✗ 读取DB1失败 - 返回None")
        except Exception as e:
            print(f"✗ 读取DB1异常: {e}")
        
        # 尝试读取其他DB
        for db_num in [1, 2, 3, 10, 100]:
            try:
                data = client.read_db(db_num, 0, 2)
                if data is not None:
                    print(f"✓ DB{db_num}可访问: {len(data)}字节")
                else:
                    print(f"✗ DB{db_num}不可访问")
            except Exception as e:
                print(f"✗ DB{db_num}读取异常: {e}")
        
        client.disconnect_plc()
        print("\n✓ PLC连接已断开")
        return True
    else:
        print("✗ PLC连接失败")
        return False


def debug_scan_method():
    """调试扫描方法"""
    print("\n" + "=" * 50)
    print("调试扫描方法")
    print("=" * 50)
    
    client = S7Client()
    
    # 获取配置并连接
    plc_config = app_config.get_plc_config()
    ip = plc_config.get("ip", "************")
    rack = plc_config.get("rack", 0)
    slot = plc_config.get("slot", 1)
    
    success = client.connect_plc(ip, rack, slot)
    if not success:
        print("✗ 无法连接PLC，跳过扫描测试")
        return False
    
    print("✓ PLC已连接，开始测试扫描方法")
    
    try:
        # 测试_detect_db_size方法
        print("\n1. 测试DB大小检测:")
        for db_num in [1, 2, 3]:
            try:
                size = client._detect_db_size(db_num, 512)
                print(f"   DB{db_num}大小: {size}字节")
            except Exception as e:
                print(f"   DB{db_num}大小检测失败: {e}")
        
        # 测试scan_data_blocks方法
        print("\n2. 测试数据块扫描:")
        try:
            db_info = client.scan_data_blocks(1, 3, 256)
            print(f"   扫描结果: {len(db_info)}个数据块")
            
            for db_num, info in db_info.items():
                print(f"   DB{db_num}: accessible={info['accessible']}, size={info['size']}")
                if info['accessible'] and info['data']:
                    print(f"     数据前8字节: {[hex(b) for b in info['data'][:8]]}")
        except Exception as e:
            print(f"   扫描失败: {e}")
            import traceback
            traceback.print_exc()
        
        # 测试discover_plc_structure方法
        print("\n3. 测试完整发现:")
        try:
            result = client.discover_plc_structure(1, 2)
            print(f"   发现结果键: {list(result.keys())}")
            
            summary = result.get("summary", {})
            print(f"   摘要: {summary}")
            
            variables = result.get("variables", [])
            print(f"   变量数量: {len(variables)}")
            
            if variables:
                print("   前3个变量:")
                for var in variables[:3]:
                    print(f"     {var.get('name', '')}: {var.get('type', '')} = {var.get('value', '')}")
        except Exception as e:
            print(f"   完整发现失败: {e}")
            import traceback
            traceback.print_exc()
    
    finally:
        client.disconnect_plc()
        print("\n✓ PLC连接已断开")
    
    return True


def debug_ui_thread():
    """调试UI线程问题"""
    print("\n" + "=" * 50)
    print("调试UI线程问题")
    print("=" * 50)
    
    # 测试DiscoveryThread类
    try:
        from main_window import DiscoveryThread
        from PySide6.QtCore import QCoreApplication
        
        print("✓ DiscoveryThread类导入成功")
        
        # 创建一个简单的Qt应用来测试信号
        app = QCoreApplication.instance()
        if app is None:
            app = QCoreApplication([])
        
        client = S7Client()
        
        # 测试线程创建
        thread = DiscoveryThread(client, 1, 2)
        print("✓ DiscoveryThread创建成功")
        
        # 测试信号连接
        def on_completed(result):
            print(f"✓ 收到完成信号: {type(result)}")
        
        def on_failed(error):
            print(f"✓ 收到失败信号: {error}")
        
        thread.discovery_completed.connect(on_completed)
        thread.discovery_failed.connect(on_failed)
        print("✓ 信号连接成功")
        
        return True
        
    except Exception as e:
        print(f"✗ UI线程测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主函数"""
    print("PLC结构发现调试工具")
    
    # 1. 测试基本连接
    connection_ok = debug_connection()
    
    if connection_ok:
        # 2. 测试扫描方法
        debug_scan_method()
    
    # 3. 测试UI线程
    debug_ui_thread()
    
    print("\n" + "=" * 50)
    print("调试完成")
    print("如果连接正常但发现功能不工作，可能的原因:")
    print("1. PLC中没有可访问的数据块")
    print("2. 权限不足，无法读取数据块")
    print("3. UI线程信号连接问题")
    print("4. 异常被静默捕获")
    print("=" * 50)


if __name__ == "__main__":
    main()
