# -*- coding: utf-8 -*-
"""
西门子S7客户端测试脚本
用于测试S7客户端的基本功能
"""

import time
import sys
from s7_client import S7Client
from config import app_config


def test_connection():
    """测试PLC连接"""
    print("=" * 50)
    print("测试PLC连接")
    print("=" * 50)
    
    client = S7Client()
    
    # 获取配置
    plc_config = app_config.get_plc_config()
    ip = plc_config.get("ip", "************")
    rack = plc_config.get("rack", 0)
    slot = plc_config.get("slot", 1)
    
    print(f"尝试连接到PLC: {ip}:{rack}:{slot}")
    
    # 连接PLC
    success = client.connect_plc(ip, rack, slot)
    
    if success:
        print("✓ PLC连接成功")
        
        # 测试连接状态
        if client.is_connected():
            print("✓ 连接状态检查通过")
        else:
            print("✗ 连接状态检查失败")
        
        # 断开连接
        client.disconnect_plc()
        print("✓ PLC连接已断开")
        
        return True
    else:
        print("✗ PLC连接失败")
        return False


def test_data_operations():
    """测试数据读写操作"""
    print("=" * 50)
    print("测试数据读写操作")
    print("=" * 50)
    
    client = S7Client()
    
    # 连接PLC
    plc_config = app_config.get_plc_config()
    success = client.connect_plc(
        plc_config.get("ip", "************"),
        plc_config.get("rack", 0),
        plc_config.get("slot", 1)
    )
    
    if not success:
        print("✗ 无法连接到PLC，跳过数据操作测试")
        return False
    
    try:
        # 测试读取变量
        print("\n测试读取变量:")
        variables = client.get_variable_list()
        
        for var_name in variables[:3]:  # 只测试前3个变量
            print(f"  读取变量 {var_name}...")
            value = client.read_variable(var_name)
            if value is not None:
                print(f"    ✓ {var_name} = {value}")
            else:
                print(f"    ✗ 读取 {var_name} 失败")
        
        # 测试写入变量（仅测试安全的变量）
        print("\n测试写入变量:")
        test_vars = {
            "counter": 100,
            "motor_status": True,
            "temperature": 25.5
        }
        
        for var_name, test_value in test_vars.items():
            if var_name in variables:
                print(f"  写入变量 {var_name} = {test_value}...")
                success = client.write_variable(var_name, test_value)
                if success:
                    print(f"    ✓ 写入成功")
                    
                    # 读取验证
                    time.sleep(0.1)
                    read_value = client.read_variable(var_name)
                    if read_value is not None:
                        print(f"    ✓ 验证读取: {var_name} = {read_value}")
                    else:
                        print(f"    ✗ 验证读取失败")
                else:
                    print(f"    ✗ 写入失败")
            else:
                print(f"  跳过变量 {var_name} (未配置)")
        
        return True
        
    except Exception as e:
        print(f"✗ 数据操作测试失败: {e}")
        return False
    
    finally:
        client.disconnect_plc()


def test_monitoring():
    """测试监控功能"""
    print("=" * 50)
    print("测试监控功能")
    print("=" * 50)
    
    client = S7Client()
    
    # 连接PLC
    plc_config = app_config.get_plc_config()
    success = client.connect_plc(
        plc_config.get("ip", "************"),
        plc_config.get("rack", 0),
        plc_config.get("slot", 1)
    )
    
    if not success:
        print("✗ 无法连接到PLC，跳过监控测试")
        return False
    
    try:
        print("开始监控测试（10秒）...")
        
        # 数据接收计数器
        data_count = 0
        
        def on_data_received(var_name, value):
            nonlocal data_count
            data_count += 1
            print(f"  接收数据: {var_name} = {value}")
        
        # 连接信号
        client.data_received.connect(on_data_received)
        
        # 开始监控
        client.start_monitoring(0.5)  # 0.5秒间隔
        
        # 等待10秒
        time.sleep(10)
        
        # 停止监控
        client.stop_monitoring()
        
        print(f"✓ 监控测试完成，共接收 {data_count} 个数据")
        return True
        
    except Exception as e:
        print(f"✗ 监控测试失败: {e}")
        return False
    
    finally:
        client.disconnect_plc()


def test_configuration():
    """测试配置功能"""
    print("=" * 50)
    print("测试配置功能")
    print("=" * 50)
    
    # 测试配置加载
    plc_config = app_config.get_plc_config()
    print(f"PLC配置: {plc_config}")
    
    variables = app_config.get_all_variables()
    print(f"变量配置数量: {len(variables)}")
    
    for var_name, var_config in list(variables.items())[:3]:
        print(f"  {var_name}: {var_config}")
    
    print("✓ 配置测试完成")
    return True


def main():
    """主测试函数"""
    print("西门子S7客户端测试程序")
    print("=" * 50)
    
    test_results = []
    
    # 运行测试
    test_results.append(("配置功能", test_configuration()))
    test_results.append(("PLC连接", test_connection()))
    
    # 只有在连接测试通过时才进行数据和监控测试
    if test_results[-1][1]:  # 如果连接测试通过
        test_results.append(("数据操作", test_data_operations()))
        test_results.append(("监控功能", test_monitoring()))
    else:
        print("\n由于PLC连接失败，跳过数据操作和监控测试")
        print("请检查:")
        print("1. PLC是否开机并连接到网络")
        print("2. IP地址是否正确")
        print("3. 防火墙设置")
        print("4. PLC是否允许外部连接")
    
    # 显示测试结果
    print("\n" + "=" * 50)
    print("测试结果汇总")
    print("=" * 50)
    
    for test_name, result in test_results:
        status = "✓ 通过" if result else "✗ 失败"
        print(f"{test_name}: {status}")
    
    # 计算通过率
    passed = sum(1 for _, result in test_results if result)
    total = len(test_results)
    print(f"\n总体结果: {passed}/{total} 测试通过")
    
    return 0 if passed == total else 1


if __name__ == "__main__":
    sys.exit(main())
