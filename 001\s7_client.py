# -*- coding: utf-8 -*-
"""
西门子S7 PLC客户端连接类
基于python-snap7库实现PLC通信功能
"""

import snap7
import struct
import threading
import time
import math
from typing import Any, Dict, List, Optional, Union
from PySide6.QtCore import QObject, Signal
from config import app_config


class S7Client(QObject):
    """西门子S7 PLC客户端类"""
    
    # 信号定义
    connection_changed = Signal(bool)  # 连接状态变化
    data_received = Signal(str, object)  # 数据接收信号 (变量名, 值)
    error_occurred = Signal(str)  # 错误信号
    
    def __init__(self):
        super().__init__()
        self.client = snap7.client.Client()
        self.connected = False
        self.config = app_config.get_plc_config()
        self.variables = app_config.get_all_variables()
        self._monitoring = False
        self._monitor_thread = None
        self._lock = threading.Lock()
    
    def connect_plc(self, ip: str = None, rack: int = None, slot: int = None) -> bool:
        """连接到PLC"""
        try:
            # 使用传入参数或配置文件中的参数
            ip = ip or self.config.get("ip", "************")
            rack = rack if rack is not None else self.config.get("rack", 0)
            slot = slot if slot is not None else self.config.get("slot", 1)
            
            # 设置连接超时
            timeout = self.config.get("timeout", 5000)
            
            print(f"正在连接PLC: {ip}, Rack: {rack}, Slot: {slot}")
            
            # 连接PLC
            self.client.connect(ip, rack, slot)
            
            # 检查连接状态
            if self.client.get_connected():
                self.connected = True
                self.connection_changed.emit(True)
                print("PLC连接成功")
                
                # 获取PLC信息
                cpu_info = self.client.get_cpu_info()
                print(f"PLC信息: {cpu_info}")
                
                return True
            else:
                self.connected = False
                self.connection_changed.emit(False)
                self.error_occurred.emit("PLC连接失败")
                return False
                
        except Exception as e:
            self.connected = False
            self.connection_changed.emit(False)
            error_msg = f"连接PLC时发生错误: {str(e)}"
            print(error_msg)
            self.error_occurred.emit(error_msg)
            return False
    
    def disconnect_plc(self) -> None:
        """断开PLC连接"""
        try:
            self.stop_monitoring()
            if self.client.get_connected():
                self.client.disconnect()
            self.connected = False
            self.connection_changed.emit(False)
            print("PLC连接已断开")
        except Exception as e:
            error_msg = f"断开PLC连接时发生错误: {str(e)}"
            print(error_msg)
            self.error_occurred.emit(error_msg)
    
    def is_connected(self) -> bool:
        """检查连接状态"""
        try:
            return self.client.get_connected() if self.client else False
        except:
            return False
    
    def read_db(self, db_number: int, start: int, size: int) -> Optional[bytearray]:
        """读取数据块"""
        try:
            if not self.is_connected():
                self.error_occurred.emit("PLC未连接")
                return None
            
            data = self.client.db_read(db_number, start, size)
            return data
        except Exception as e:
            error_msg = f"读取DB{db_number}失败: {str(e)}"
            print(error_msg)
            self.error_occurred.emit(error_msg)
            return None
    
    def write_db(self, db_number: int, start: int, data: bytearray) -> bool:
        """写入数据块"""
        try:
            if not self.is_connected():
                self.error_occurred.emit("PLC未连接")
                return False
            
            self.client.db_write(db_number, start, data)
            return True
        except Exception as e:
            error_msg = f"写入DB{db_number}失败: {str(e)}"
            print(error_msg)
            self.error_occurred.emit(error_msg)
            return False
    
    def read_variable(self, var_name: str) -> Optional[Any]:
        """读取变量值"""
        var_config = self.variables.get(var_name)
        if not var_config:
            self.error_occurred.emit(f"未找到变量配置: {var_name}")
            return None
        
        try:
            db_number = var_config["db"]
            offset = var_config["offset"]
            var_type = var_config["type"]
            
            # 根据数据类型确定读取大小
            size_map = {
                "BOOL": 1,
                "BYTE": 1,
                "INT": 2,
                "DINT": 4,
                "REAL": 4,
                "STRING": var_config.get("length", 254) + 2
            }
            
            size = size_map.get(var_type, 4)
            data = self.read_db(db_number, offset, size)
            
            if data is None:
                return None
            
            # 解析数据
            value = self._parse_data(data, var_type, var_config)
            return value
            
        except Exception as e:
            error_msg = f"读取变量{var_name}失败: {str(e)}"
            print(error_msg)
            self.error_occurred.emit(error_msg)
            return None
    
    def write_variable(self, var_name: str, value: Any) -> bool:
        """写入变量值"""
        var_config = self.variables.get(var_name)
        if not var_config:
            self.error_occurred.emit(f"未找到变量配置: {var_name}")
            return False
        
        try:
            db_number = var_config["db"]
            offset = var_config["offset"]
            var_type = var_config["type"]
            
            # 将值转换为字节数据
            data = self._value_to_bytes(value, var_type, var_config)
            if data is None:
                return False
            
            return self.write_db(db_number, offset, data)
            
        except Exception as e:
            error_msg = f"写入变量{var_name}失败: {str(e)}"
            print(error_msg)
            self.error_occurred.emit(error_msg)
            return False

    def _parse_data(self, data: bytearray, var_type: str, var_config: Dict) -> Any:
        """解析字节数据为对应类型的值"""
        try:
            if var_type == "BOOL":
                bit = var_config.get("bit", 0)
                byte_val = data[0]
                return bool(byte_val & (1 << bit))

            elif var_type == "BYTE":
                return data[0]

            elif var_type == "INT":
                return struct.unpack(">h", data[:2])[0]  # 大端序有符号短整型

            elif var_type == "DINT":
                return struct.unpack(">i", data[:4])[0]  # 大端序有符号整型

            elif var_type == "REAL":
                return struct.unpack(">f", data[:4])[0]  # 大端序浮点型

            elif var_type == "STRING":
                max_len = data[0]
                actual_len = data[1]
                return data[2:2+actual_len].decode('utf-8', errors='ignore')

            else:
                self.error_occurred.emit(f"不支持的数据类型: {var_type}")
                return None

        except Exception as e:
            self.error_occurred.emit(f"解析数据失败: {str(e)}")
            return None

    def _value_to_bytes(self, value: Any, var_type: str, var_config: Dict) -> Optional[bytearray]:
        """将值转换为字节数据"""
        try:
            if var_type == "BOOL":
                bit = var_config.get("bit", 0)
                # 读取当前字节值，只修改指定位
                db_number = var_config["db"]
                offset = var_config["offset"]
                current_data = self.read_db(db_number, offset, 1)
                if current_data is None:
                    return None

                byte_val = current_data[0]
                if value:
                    byte_val |= (1 << bit)  # 设置位
                else:
                    byte_val &= ~(1 << bit)  # 清除位

                return bytearray([byte_val])

            elif var_type == "BYTE":
                return bytearray([int(value) & 0xFF])

            elif var_type == "INT":
                return bytearray(struct.pack(">h", int(value)))

            elif var_type == "DINT":
                return bytearray(struct.pack(">i", int(value)))

            elif var_type == "REAL":
                return bytearray(struct.pack(">f", float(value)))

            elif var_type == "STRING":
                str_value = str(value)
                max_len = var_config.get("length", 254)
                str_bytes = str_value.encode('utf-8')[:max_len]
                actual_len = len(str_bytes)

                result = bytearray([max_len, actual_len])
                result.extend(str_bytes)
                result.extend(b'\x00' * (max_len - actual_len))  # 填充空字节
                return result

            else:
                self.error_occurred.emit(f"不支持的数据类型: {var_type}")
                return None

        except Exception as e:
            self.error_occurred.emit(f"转换数据失败: {str(e)}")
            return None

    def start_monitoring(self, interval: float = 1.0) -> None:
        """开始监控变量"""
        if self._monitoring:
            return

        self._monitoring = True
        self._monitor_thread = threading.Thread(target=self._monitor_loop, args=(interval,))
        self._monitor_thread.daemon = True
        self._monitor_thread.start()
        print("开始监控变量")

    def stop_monitoring(self) -> None:
        """停止监控变量"""
        self._monitoring = False
        if self._monitor_thread and self._monitor_thread.is_alive():
            self._monitor_thread.join(timeout=2.0)
        print("停止监控变量")

    def _monitor_loop(self, interval: float) -> None:
        """监控循环"""
        while self._monitoring and self.is_connected():
            try:
                for var_name in self.variables.keys():
                    if not self._monitoring:
                        break

                    value = self.read_variable(var_name)
                    if value is not None:
                        self.data_received.emit(var_name, value)

                time.sleep(interval)

            except Exception as e:
                error_msg = f"监控循环错误: {str(e)}"
                print(error_msg)
                self.error_occurred.emit(error_msg)
                break

    def get_variable_list(self) -> List[str]:
        """获取所有变量名列表"""
        return list(self.variables.keys())

    def get_variable_info(self, var_name: str) -> Optional[Dict]:
        """获取变量信息"""
        return self.variables.get(var_name)

    def read_bool(self, db_number: int, offset: int, bit: int) -> Optional[bool]:
        """读取布尔值"""
        try:
            if not self.is_connected():
                return None

            # 读取一个字节
            data = self.plc.read_area(snap7.types.Areas.DB, db_number, offset, 1)
            if data:
                byte_val = data[0]
                return bool(byte_val & (1 << bit))
            return None
        except Exception as e:
            print(f"读取布尔值失败 DB{db_number}.{offset}.{bit}: {e}")
            return None

    def read_byte(self, db_number: int, offset: int) -> Optional[int]:
        """读取字节值"""
        try:
            if not self.is_connected():
                return None

            data = self.plc.read_area(snap7.types.Areas.DB, db_number, offset, 1)
            return data[0] if data else None
        except Exception as e:
            print(f"读取字节值失败 DB{db_number}.{offset}: {e}")
            return None

    def read_int(self, db_number: int, offset: int) -> Optional[int]:
        """读取16位整数值"""
        try:
            if not self.is_connected():
                return None

            data = self.plc.read_area(snap7.types.Areas.DB, db_number, offset, 2)
            if data and len(data) >= 2:
                # 大端字节序
                return int.from_bytes(data[:2], byteorder='big', signed=True)
            return None
        except Exception as e:
            print(f"读取整数值失败 DB{db_number}.{offset}: {e}")
            return None

    def read_dint(self, db_number: int, offset: int) -> Optional[int]:
        """读取32位双整数值"""
        try:
            if not self.is_connected():
                return None

            data = self.plc.read_area(snap7.types.Areas.DB, db_number, offset, 4)
            if data and len(data) >= 4:
                # 大端字节序
                return int.from_bytes(data[:4], byteorder='big', signed=True)
            return None
        except Exception as e:
            print(f"读取双整数值失败 DB{db_number}.{offset}: {e}")
            return None

    def read_real(self, db_number: int, offset: int) -> Optional[float]:
        """读取32位实数值"""
        try:
            if not self.is_connected():
                return None

            data = self.plc.read_area(snap7.types.Areas.DB, db_number, offset, 4)
            if data and len(data) >= 4:
                import struct
                # 大端字节序的IEEE 754浮点数
                return struct.unpack('>f', data[:4])[0]
            return None
        except Exception as e:
            print(f"读取实数值失败 DB{db_number}.{offset}: {e}")
            return None

    def read_area(self, db_number: int, offset: int, size: int) -> Optional[bytearray]:
        """读取指定区域的原始数据"""
        try:
            if not self.is_connected():
                return None

            return self.plc.read_area(snap7.types.Areas.DB, db_number, offset, size)
        except Exception as e:
            print(f"读取区域数据失败 DB{db_number}.{offset}[{size}]: {e}")
            return None

    def scan_data_blocks(self, start_db: int = 1, end_db: int = 100, max_size: int = 1024) -> Dict[int, Dict]:
        """扫描PLC中的数据块

        Args:
            start_db: 起始DB号
            end_db: 结束DB号
            max_size: 每个DB的最大扫描大小

        Returns:
            Dict[int, Dict]: {db_number: {"size": size, "accessible": True/False, "data": bytearray}}
        """
        if not self.is_connected():
            self.error_occurred.emit("PLC未连接")
            return {}

        db_info = {}
        print(f"开始扫描DB{start_db}到DB{end_db}...")

        for db_num in range(start_db, end_db + 1):
            try:
                # 尝试读取DB的前几个字节来检测是否存在
                test_data = self.client.db_read(db_num, 0, 4)
                if test_data is not None:
                    # DB存在，尝试确定其大小
                    db_size = self._detect_db_size(db_num, max_size)

                    # 读取完整数据
                    full_data = None
                    if db_size > 0:
                        try:
                            full_data = self.client.db_read(db_num, 0, db_size)
                        except:
                            pass

                    db_info[db_num] = {
                        "size": db_size,
                        "accessible": True,
                        "data": full_data,
                        "scan_time": time.time()
                    }

                    print(f"  发现DB{db_num}: 大小={db_size}字节")

            except Exception as e:
                # DB不存在或无法访问
                db_info[db_num] = {
                    "size": 0,
                    "accessible": False,
                    "data": None,
                    "error": str(e)
                }

        print(f"扫描完成，发现{len([db for db in db_info.values() if db['accessible']])}个可访问的数据块")
        return db_info

    def _detect_db_size(self, db_number: int, max_size: int = 1024) -> int:
        """检测数据块的实际大小"""
        try:
            # 二分法查找DB的实际大小
            low, high = 0, max_size
            actual_size = 0

            while low <= high:
                mid = (low + high) // 2
                try:
                    # 尝试读取到mid位置
                    self.client.db_read(db_number, 0, mid)
                    actual_size = mid
                    low = mid + 1
                except:
                    high = mid - 1

            return actual_size

        except Exception:
            return 0

    def analyze_db_data(self, db_number: int, data: bytearray) -> List[Dict]:
        """分析数据块内容，尝试识别可能的变量

        Args:
            db_number: 数据块号
            data: 数据块内容

        Returns:
            List[Dict]: 可能的变量列表
        """
        if not data:
            return []

        variables = []
        processed_offsets = set()  # 避免重复处理同一偏移

        # 优化的变量识别策略：按字对齐，减少误报
        offset = 0
        while offset < len(data):
            if offset in processed_offsets:
                offset += 1
                continue

            # 1. 检查STRING类型（优先级最高，因为有明确的结构）
            if offset + 1 < len(data):
                try:
                    max_len = data[offset]
                    actual_len = data[offset + 1]
                    # 更严格的STRING检测条件
                    if (4 <= max_len <= 254 and 0 <= actual_len <= max_len and
                        offset + 2 + actual_len <= len(data) and actual_len > 0):
                        str_bytes = data[offset + 2:offset + 2 + actual_len]
                        try:
                            str_val = str_bytes.decode('utf-8')
                            # 检查是否包含足够的可打印字符
                            printable_count = sum(1 for c in str_val if c.isprintable())
                            if printable_count >= len(str_val) * 0.8 and len(str_val) >= 2:
                                variables.append({
                                    "name": f"DB{db_number}_STRING_{offset}",
                                    "db": db_number,
                                    "offset": offset,
                                    "type": "STRING",
                                    "length": max_len,
                                    "value": str_val,
                                    "description": f"DB{db_number}偏移{offset}的字符串值"
                                })
                                # 跳过整个字符串区域
                                for i in range(offset, min(offset + 2 + max_len, len(data))):
                                    processed_offsets.add(i)
                                offset = min(offset + 2 + max_len, len(data))
                                continue
                        except:
                            pass
                except:
                    pass

            # 2. 检查REAL类型（4字节浮点数，按4字节对齐）
            if offset % 4 == 0 and offset + 3 < len(data):
                try:
                    real_val = struct.unpack(">f", data[offset:offset+4])[0]
                    # 更严格的浮点数检测
                    if (not (math.isnan(real_val) or math.isinf(real_val)) and
                        abs(real_val) < 1e6 and abs(real_val) > 1e-6):
                        variables.append({
                            "name": f"DB{db_number}_REAL_{offset}",
                            "db": db_number,
                            "offset": offset,
                            "type": "REAL",
                            "value": round(real_val, 6),
                            "description": f"DB{db_number}偏移{offset}的浮点数值"
                        })
                        # 标记已处理的字节
                        for i in range(offset, offset + 4):
                            processed_offsets.add(i)
                        offset += 4
                        continue
                except:
                    pass

            # 3. 检查DINT类型（4字节整数，按4字节对齐）
            if offset % 4 == 0 and offset + 3 < len(data):
                try:
                    dint_val = struct.unpack(">i", data[offset:offset+4])[0]
                    # 更合理的DINT范围检测
                    if abs(dint_val) < 1000000:  # 限制在合理范围内
                        variables.append({
                            "name": f"DB{db_number}_DINT_{offset}",
                            "db": db_number,
                            "offset": offset,
                            "type": "DINT",
                            "value": dint_val,
                            "description": f"DB{db_number}偏移{offset}的长整数值"
                        })
                        # 标记已处理的字节
                        for i in range(offset, offset + 4):
                            processed_offsets.add(i)
                        offset += 4
                        continue
                except:
                    pass

            # 4. 检查INT类型（2字节整数，按2字节对齐）
            if offset % 2 == 0 and offset + 1 < len(data):
                try:
                    int_val = struct.unpack(">h", data[offset:offset+2])[0]
                    # 更合理的INT范围检测
                    if abs(int_val) < 10000:  # 限制在合理范围内
                        variables.append({
                            "name": f"DB{db_number}_INT_{offset}",
                            "db": db_number,
                            "offset": offset,
                            "type": "INT",
                            "value": int_val,
                            "description": f"DB{db_number}偏移{offset}的整数值"
                        })
                        # 标记已处理的字节
                        for i in range(offset, offset + 2):
                            processed_offsets.add(i)
                        offset += 2
                        continue
                except:
                    pass

            # 5. 检查BOOL类型（只检查有意义的字节）
            if offset not in processed_offsets and offset < len(data):
                byte_val = data[offset]
                # 只有当字节值为0x00或0x01时才认为可能是布尔值
                if byte_val in [0x00, 0x01]:
                    # 只添加字节级别的布尔值，不添加位级别
                    variables.append({
                        "name": f"DB{db_number}_BOOL_{offset}",
                        "db": db_number,
                        "offset": offset,
                        "type": "BOOL",
                        "bit": 0,
                        "value": bool(byte_val),
                        "description": f"DB{db_number}偏移{offset}的布尔值"
                    })
                processed_offsets.add(offset)

            offset += 1

        return variables

    def discover_plc_structure(self, start_db: int = 1, end_db: int = 50, mode: str = "smart") -> Dict:
        """发现PLC结构，包括数据块和可能的变量

        Args:
            start_db: 起始DB号
            end_db: 结束DB号
            mode: 扫描模式 ("smart": 智能模式, "detailed": 详细模式, "summary": 摘要模式)

        Returns:
            Dict: {"data_blocks": {}, "variables": [], "summary": {}}
        """
        if not self.is_connected():
            self.error_occurred.emit("PLC未连接")
            return {}

        print(f"开始发现PLC结构 (模式: {mode})...")

        # 根据模式调整扫描参数
        if mode == "summary":
            # 摘要模式：只扫描数据块信息，不分析变量
            max_size = 64  # 只读取前64字节用于基本分析
        elif mode == "smart":
            # 智能模式：适中的扫描深度
            max_size = 512
        else:  # detailed
            # 详细模式：完整扫描
            max_size = 1024

        # 扫描数据块
        db_info = self.scan_data_blocks(start_db, end_db, max_size)

        # 分析每个数据块的内容
        all_variables = []
        if mode != "summary":
            for db_num, db_data in db_info.items():
                if db_data["accessible"] and db_data["data"]:
                    variables = self.analyze_db_data(db_num, db_data["data"])

                    # 在智能模式下，限制每个DB的变量数量
                    if mode == "smart" and len(variables) > 50:
                        # 优先保留不同类型的变量
                        filtered_vars = self._filter_variables_smart(variables)
                        all_variables.extend(filtered_vars)
                    else:
                        all_variables.extend(variables)

        # 生成摘要
        accessible_dbs = [db for db, info in db_info.items() if info["accessible"]]
        total_size = sum(info["size"] for info in db_info.values() if info["accessible"])

        summary = {
            "scan_mode": mode,
            "total_dbs_scanned": len(db_info),
            "accessible_dbs": len(accessible_dbs),
            "accessible_db_numbers": accessible_dbs,
            "total_data_size": total_size,
            "total_variables_found": len(all_variables),
            "variable_types": {}
        }

        # 统计变量类型
        for var in all_variables:
            var_type = var["type"]
            summary["variable_types"][var_type] = summary["variable_types"].get(var_type, 0) + 1

        result = {
            "data_blocks": db_info,
            "variables": all_variables,
            "summary": summary
        }

        print(f"发现完成: {summary['accessible_dbs']}个数据块, {summary['total_variables_found']}个可能的变量")

        return result

    def _filter_variables_smart(self, variables: List[Dict]) -> List[Dict]:
        """智能过滤变量，保留最有价值的变量"""
        if len(variables) <= 50:
            return variables

        filtered = []
        type_counts = {}

        # 按类型分组
        by_type = {}
        for var in variables:
            var_type = var["type"]
            if var_type not in by_type:
                by_type[var_type] = []
            by_type[var_type].append(var)

        # 每种类型最多保留一定数量
        type_limits = {
            "STRING": 10,  # 字符串通常比较重要
            "REAL": 15,    # 浮点数可能是传感器数据
            "DINT": 10,    # 长整数
            "INT": 10,     # 整数
            "BOOL": 5      # 布尔值
        }

        for var_type, vars_list in by_type.items():
            limit = type_limits.get(var_type, 5)
            # 按偏移量排序，取前N个
            sorted_vars = sorted(vars_list, key=lambda x: x["offset"])
            filtered.extend(sorted_vars[:limit])

        return filtered[:50]  # 总数不超过50个

    def __del__(self):
        """析构函数"""
        try:
            self.disconnect_plc()
        except:
            pass
