#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试优化后的PLC结构发现功能
"""

import sys
import json
from s7_client import S7Client

def test_discovery_modes():
    """测试不同的发现模式"""
    
    # 创建S7客户端
    client = S7Client()
    
    # 连接PLC
    print("正在连接PLC...")
    success = client.connect_plc("172.53.100.1", 0, 1)
    
    if not success:
        print("❌ PLC连接失败")
        return
    
    print("✅ PLC连接成功")
    
    # 测试不同模式
    modes = [
        ("summary", "摘要模式"),
        ("smart", "智能模式"),
        ("detailed", "详细模式")
    ]
    
    results = {}
    
    for mode, mode_name in modes:
        print(f"\n{'='*50}")
        print(f"测试 {mode_name} ({mode})")
        print(f"{'='*50}")
        
        try:
            result = client.discover_plc_structure(1, 10, mode)
            
            if result:
                summary = result.get("summary", {})
                print(f"✅ {mode_name} 完成:")
                print(f"   - 扫描DB数量: {summary.get('total_dbs_scanned', 0)}")
                print(f"   - 可访问DB数量: {summary.get('accessible_dbs', 0)}")
                print(f"   - 可访问DB编号: {summary.get('accessible_db_numbers', [])}")
                print(f"   - 总数据大小: {summary.get('total_data_size', 0)} 字节")
                print(f"   - 发现变量数量: {summary.get('total_variables_found', 0)}")
                
                var_types = summary.get('variable_types', {})
                if var_types:
                    print("   - 变量类型分布:")
                    for var_type, count in var_types.items():
                        print(f"     * {var_type}: {count}个")
                
                results[mode] = result
                
            else:
                print(f"❌ {mode_name} 失败: 无结果")
                
        except Exception as e:
            print(f"❌ {mode_name} 失败: {e}")
    
    # 比较结果
    print(f"\n{'='*50}")
    print("模式比较")
    print(f"{'='*50}")
    
    for mode, mode_name in modes:
        if mode in results:
            summary = results[mode].get("summary", {})
            var_count = summary.get('total_variables_found', 0)
            print(f"{mode_name:12}: {var_count:6}个变量")
    
    # 保存智能模式结果
    if "smart" in results:
        print(f"\n保存智能模式结果到文件...")
        try:
            with open("smart_discovery_result.json", "w", encoding="utf-8") as f:
                json.dump(results["smart"], f, indent=2, ensure_ascii=False, default=str)
            print("✅ 结果已保存到 smart_discovery_result.json")
        except Exception as e:
            print(f"❌ 保存失败: {e}")
    
    # 断开连接
    client.disconnect_plc()
    print("\n🔌 PLC连接已断开")

if __name__ == "__main__":
    test_discovery_modes()
