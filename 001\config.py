# -*- coding: utf-8 -*-
"""
西门子S7 PLC客户端配置管理模块
"""

import json
import os
from typing import Dict, Any


class S7Config:
    """S7 PLC连接配置管理类"""
    
    def __init__(self, config_file: str = "s7_config.json"):
        self.config_file = config_file
        self.default_config = {
            "plc": {
                "ip": "************",
                "rack": 0,
                "slot": 1,
                "timeout": 5000,  # 毫秒
                "connection_type": "PG"  # PG, OP, S7_BASIC
            },
            "data_blocks": {
                "DB1": {
                    "number": 1,
                    "start": 0,
                    "size": 100,
                    "description": "测试数据块1"
                },
                "DB2": {
                    "number": 2,
                    "start": 0,
                    "size": 50,
                    "description": "测试数据块2"
                }
            },
            "variables": {
                "temperature": {
                    "db": 1,
                    "offset": 0,
                    "type": "REAL",
                    "description": "温度值"
                },
                "pressure": {
                    "db": 1,
                    "offset": 4,
                    "type": "REAL",
                    "description": "压力值"
                },
                "motor_status": {
                    "db": 1,
                    "offset": 8,
                    "type": "BOOL",
                    "bit": 0,
                    "description": "电机状态"
                },
                "counter": {
                    "db": 1,
                    "offset": 10,
                    "type": "INT",
                    "description": "计数器"
                }
            },
            "ui": {
                "refresh_interval": 1000,  # 毫秒
                "auto_connect": False,
                "log_level": "INFO"
            }
        }
        self.config = self.load_config()
    
    def load_config(self) -> Dict[str, Any]:
        """加载配置文件"""
        if os.path.exists(self.config_file):
            try:
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                # 合并默认配置，确保所有必要的键都存在
                return self._merge_config(self.default_config, config)
            except (json.JSONDecodeError, IOError) as e:
                print(f"加载配置文件失败: {e}")
                return self.default_config.copy()
        else:
            # 如果配置文件不存在，创建默认配置文件
            self.save_config(self.default_config)
            return self.default_config.copy()
    
    def save_config(self, config: Dict[str, Any] = None) -> bool:
        """保存配置文件"""
        if config is None:
            config = self.config
        
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, indent=4, ensure_ascii=False)
            return True
        except IOError as e:
            print(f"保存配置文件失败: {e}")
            return False
    
    def _merge_config(self, default: Dict[str, Any], user: Dict[str, Any]) -> Dict[str, Any]:
        """合并用户配置和默认配置"""
        result = default.copy()
        for key, value in user.items():
            if key in result and isinstance(result[key], dict) and isinstance(value, dict):
                result[key] = self._merge_config(result[key], value)
            else:
                result[key] = value
        return result
    
    def get_plc_config(self) -> Dict[str, Any]:
        """获取PLC连接配置"""
        return self.config.get("plc", {})
    
    def get_variable_config(self, var_name: str) -> Dict[str, Any]:
        """获取变量配置"""
        return self.config.get("variables", {}).get(var_name, {})
    
    def get_all_variables(self) -> Dict[str, Dict[str, Any]]:
        """获取所有变量配置"""
        return self.config.get("variables", {})
    
    def update_plc_config(self, **kwargs) -> None:
        """更新PLC连接配置"""
        plc_config = self.config.setdefault("plc", {})
        plc_config.update(kwargs)
    
    def get_ui_config(self) -> Dict[str, Any]:
        """获取UI配置"""
        return self.config.get("ui", {})


# 全局配置实例
app_config = S7Config()
