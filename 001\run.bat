@echo off
echo 西门子S7 PLC客户端测试程序
echo ================================

REM 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo 错误: 未找到Python，请先安装Python 3.7+
    pause
    exit /b 1
)

REM 检查依赖是否安装
echo 检查依赖库...
python -c "import PySide6, snap7" >nul 2>&1
if errorlevel 1 (
    echo 正在安装依赖库...
    pip install -r requirements.txt
    if errorlevel 1 (
        echo 错误: 依赖安装失败
        pause
        exit /b 1
    )
)

REM 启动程序
echo 启动程序...
python main.py

pause
