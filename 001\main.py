# -*- coding: utf-8 -*-
"""
西门子S7 PLC客户端测试程序
主程序入口点

使用方法:
python main.py

依赖:
- PySide6
- python-snap7
"""

import sys
import os
from PySide6.QtWidgets import QApplication, QMessageBox
from PySide6.QtCore import Qt
from PySide6.QtGui import QIcon

from main_window import MainWindow


def check_dependencies():
    """检查依赖库是否安装"""
    missing_deps = []
    
    try:
        import snap7
    except ImportError:
        missing_deps.append("python-snap7")
    
    try:
        import PySide6
    except ImportError:
        missing_deps.append("PySide6")
    
    if missing_deps:
        print("缺少以下依赖库:")
        for dep in missing_deps:
            print(f"  - {dep}")
        print("\n请使用以下命令安装:")
        print("pip install -r requirements.txt")
        return False
    
    return True


def setup_application():
    """设置应用程序"""
    # 创建应用程序实例
    app = QApplication(sys.argv)
    
    # 设置应用程序信息
    app.setApplicationName("西门子S7 PLC客户端")
    app.setApplicationVersion("1.0.0")
    app.setOrganizationName("PLC测试工具")
    
    # 设置应用程序图标（如果有的话）
    # app.setWindowIcon(QIcon("icon.ico"))
    
    # 设置样式
    app.setStyle("Fusion")  # 使用Fusion样式，跨平台一致性更好
    
    return app


def main():
    """主函数"""
    print("西门子S7 PLC客户端测试程序")
    print("=" * 40)
    
    # 检查依赖
    if not check_dependencies():
        input("按回车键退出...")
        return 1
    
    try:
        # 创建应用程序
        app = setup_application()
        
        # 创建主窗口
        main_window = MainWindow()
        
        # 显示主窗口
        main_window.show()
        
        print("应用程序已启动")
        print("使用说明:")
        print("1. 在左侧面板配置PLC连接参数")
        print("2. 点击'连接'按钮连接到PLC")
        print("3. 连接成功后可以开始监控变量或写入数据")
        print("4. 在'实时数据'标签页查看变量值")
        print("5. 在'变量配置'标签页查看变量配置信息")
        
        # 运行应用程序
        exit_code = app.exec()
        
        print("应用程序已退出")
        return exit_code
        
    except Exception as e:
        print(f"启动应用程序时发生错误: {e}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == "__main__":
    sys.exit(main())
