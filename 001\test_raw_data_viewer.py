#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试原始数据查看器功能
"""

import sys
from PySide6.QtWidgets import QApplication
from main_window import <PERSON><PERSON>ataViewer

def create_test_data():
    """创建测试数据"""
    # 模拟发现结果
    test_data = {
        "summary": {
            "scan_mode": "smart",
            "total_dbs_scanned": 10,
            "accessible_dbs": 3,
            "accessible_db_numbers": [1, 3, 8],
            "total_data_size": 88,
            "total_variables_found": 25,
            "variable_types": {
                "BOOL": 5,
                "INT": 8,
                "DINT": 10,
                "REAL": 2
            }
        },
        "data_blocks": {
            1: {
                "accessible": True,
                "size": 4,
                "data": bytearray([0x12, 0x34, 0x56, 0x78])
            },
            3: {
                "accessible": True,
                "size": 12,
                "data": bytearray([
                    0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07,
                    0x08, 0x09, 0x0A, 0x0B
                ])
            },
            8: {
                "accessible": True,
                "size": 72,
                "data": bytearray([
                    # 模拟包含各种数据类型的数据块
                    0x48, 0x65, 0x6C, 0x6C, 0x6F, 0x20, 0x57, 0x6F,  # "Hello Wo"
                    0x72, 0x6C, 0x64, 0x21, 0x00, 0x00, 0x00, 0x00,  # "rld!"
                    0x00, 0x64, 0x00, 0xC8, 0x01, 0x2C, 0x01, 0x90,  # INT values
                    0x00, 0x00, 0x03, 0xE8, 0x00, 0x00, 0x07, 0xD0,  # DINT values
                    0x42, 0x48, 0x00, 0x00, 0x43, 0x96, 0x00, 0x00,  # REAL values
                    0x01, 0x00, 0x01, 0x01, 0x00, 0x00, 0x01, 0x01,  # BOOL values
                    0xFF, 0xFF, 0xFF, 0xFF, 0x80, 0x00, 0x00, 0x01,  # Edge cases
                    0x7F, 0xFF, 0xFF, 0xFF, 0x00, 0x00, 0x00, 0x00,  # More edge cases
                    0x41, 0x42, 0x43, 0x44, 0x45, 0x46, 0x47, 0x48   # ASCII "ABCDEFGH"
                ])
            }
        },
        "variables": []  # 变量列表（这里为了简化测试暂时为空）
    }
    
    return test_data

def test_raw_data_viewer():
    """测试原始数据查看器"""
    app = QApplication(sys.argv)
    
    # 创建测试数据
    test_discovery_result = create_test_data()
    
    # 创建原始数据查看器
    viewer = RawDataViewer(test_discovery_result)
    
    print("原始数据查看器测试")
    print("=" * 50)
    print("测试数据包含:")
    print("- DB1: 4字节数据 (0x12, 0x34, 0x56, 0x78)")
    print("- DB3: 12字节数据 (0x00-0x0B)")
    print("- DB8: 72字节数据 (包含文本、数值、布尔值等)")
    print()
    print("功能测试:")
    print("1. 选择不同的数据块")
    print("2. 切换显示格式 (十六进制/十进制/二进制/ASCII)")
    print("3. 导出原始数据")
    print("4. 查看数据的详细格式化显示")
    print()
    print("窗口已打开，请在GUI中测试各项功能...")
    
    # 显示查看器
    viewer.show()
    
    # 运行应用
    return app.exec()

if __name__ == "__main__":
    sys.exit(test_raw_data_viewer())
