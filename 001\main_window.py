# -*- coding: utf-8 -*-
"""
西门子S7 PLC客户端主窗口
基于PySide6实现的图形用户界面
"""

import sys
import time
from typing import Dict, Any
from PySide6.QtWidgets import (
    QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, QGridLayout,
    QGroupBox, QLabel, QLineEdit, QPushButton, QSpinBox, QDoubleSpinBox,
    QCheckBox, QTextEdit, QTableWidget, QTableWidgetItem, QComboBox,
    QSplitter, QTabWidget, QMessageBox, QStatusBar, QProgressBar,
    QDialog, QScrollArea, QFrame, QFileDialog
)
from PySide6.QtCore import Qt, QTimer, Signal, Slot, QThread
from PySide6.QtGui import QFont, QIcon, QPixmap, QTextCursor

from s7_client import S7Client
from config import app_config


class DiscoveryThread(QThread):
    """PLC结构发现线程"""
    discovery_completed = Signal(dict)
    discovery_failed = Signal(str)

    def __init__(self, s7_client, start_db, end_db, scan_mode="smart"):
        super().__init__()
        self.s7_client = s7_client
        self.start_db = start_db
        self.end_db = end_db
        self.scan_mode = scan_mode

    def run(self):
        """运行发现任务"""
        try:
            print(f"DiscoveryThread: 开始发现 DB{self.start_db}-DB{self.end_db} (模式: {self.scan_mode})")
            result = self.s7_client.discover_plc_structure(self.start_db, self.end_db, self.scan_mode)
            print(f"DiscoveryThread: 发现完成，发射完成信号")
            self.discovery_completed.emit(result)
        except Exception as e:
            print(f"DiscoveryThread: 发现失败 - {e}")
            import traceback
            traceback.print_exc()
            self.discovery_failed.emit(str(e))


class MainWindow(QMainWindow):
    """主窗口类"""
    
    def __init__(self):
        super().__init__()
        self.s7_client = S7Client()
        self.setup_ui()
        self.setup_connections()
        self.setup_timer()
        self.load_config()
        
        # 状态变量
        self.monitoring = False
        
    def setup_ui(self):
        """设置用户界面"""
        self.setWindowTitle("西门子S7 PLC客户端测试程序")
        self.setMinimumSize(1000, 700)
        
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建主布局
        main_layout = QHBoxLayout(central_widget)
        
        # 创建分割器
        splitter = QSplitter(Qt.Orientation.Horizontal)
        main_layout.addWidget(splitter)
        
        # 左侧控制面板
        left_panel = self.create_control_panel()
        splitter.addWidget(left_panel)
        
        # 右侧数据显示面板
        right_panel = self.create_data_panel()
        splitter.addWidget(right_panel)
        
        # 设置分割器比例
        splitter.setSizes([350, 650])
        
        # 创建状态栏
        self.setup_status_bar()
    
    def create_control_panel(self) -> QWidget:
        """创建控制面板"""
        panel = QWidget()
        layout = QVBoxLayout(panel)
        
        # 连接配置组
        conn_group = QGroupBox("PLC连接配置")
        conn_layout = QGridLayout(conn_group)
        
        # IP地址
        conn_layout.addWidget(QLabel("IP地址:"), 0, 0)
        self.ip_edit = QLineEdit()
        self.ip_edit.setPlaceholderText("************")
        conn_layout.addWidget(self.ip_edit, 0, 1)
        
        # Rack
        conn_layout.addWidget(QLabel("Rack:"), 1, 0)
        self.rack_spin = QSpinBox()
        self.rack_spin.setRange(0, 7)
        conn_layout.addWidget(self.rack_spin, 1, 1)
        
        # Slot
        conn_layout.addWidget(QLabel("Slot:"), 2, 0)
        self.slot_spin = QSpinBox()
        self.slot_spin.setRange(0, 31)
        conn_layout.addWidget(self.slot_spin, 2, 1)
        
        # 连接按钮
        self.connect_btn = QPushButton("连接")
        self.connect_btn.setStyleSheet("QPushButton { background-color: #4CAF50; color: white; font-weight: bold; }")
        conn_layout.addWidget(self.connect_btn, 3, 0, 1, 2)
        
        layout.addWidget(conn_group)
        
        # 监控控制组
        monitor_group = QGroupBox("监控控制")
        monitor_layout = QVBoxLayout(monitor_group)
        
        # 监控间隔
        interval_layout = QHBoxLayout()
        interval_layout.addWidget(QLabel("监控间隔(秒):"))
        self.interval_spin = QDoubleSpinBox()
        self.interval_spin.setRange(0.1, 10.0)
        self.interval_spin.setValue(1.0)
        self.interval_spin.setSingleStep(0.1)
        interval_layout.addWidget(self.interval_spin)
        monitor_layout.addLayout(interval_layout)
        
        # 监控按钮
        self.monitor_btn = QPushButton("开始监控")
        self.monitor_btn.setEnabled(False)
        monitor_layout.addWidget(self.monitor_btn)
        
        layout.addWidget(monitor_group)
        
        # 变量写入组
        write_group = QGroupBox("变量写入")
        write_layout = QGridLayout(write_group)
        
        # 变量选择
        write_layout.addWidget(QLabel("变量:"), 0, 0)
        self.var_combo = QComboBox()
        write_layout.addWidget(self.var_combo, 0, 1)
        
        # 值输入
        write_layout.addWidget(QLabel("值:"), 1, 0)
        self.value_edit = QLineEdit()
        write_layout.addWidget(self.value_edit, 1, 1)
        
        # 写入按钮
        self.write_btn = QPushButton("写入")
        self.write_btn.setEnabled(False)
        write_layout.addWidget(self.write_btn, 2, 0, 1, 2)

        layout.addWidget(write_group)

        # PLC结构发现组
        discover_group = QGroupBox("PLC结构发现")
        discover_layout = QVBoxLayout(discover_group)

        # 扫描范围设置
        range_layout = QHBoxLayout()
        range_layout.addWidget(QLabel("DB范围:"))

        self.start_db_spin = QSpinBox()
        self.start_db_spin.setRange(1, 999)
        self.start_db_spin.setValue(1)
        range_layout.addWidget(self.start_db_spin)

        range_layout.addWidget(QLabel("到"))

        self.end_db_spin = QSpinBox()
        self.end_db_spin.setRange(1, 999)
        self.end_db_spin.setValue(50)
        range_layout.addWidget(self.end_db_spin)

        discover_layout.addLayout(range_layout)

        # 扫描模式选择
        mode_layout = QHBoxLayout()
        mode_layout.addWidget(QLabel("扫描模式:"))
        self.scan_mode_combo = QComboBox()
        self.scan_mode_combo.addItems([
            "智能模式 (推荐)",
            "摘要模式 (仅DB信息)",
            "详细模式 (完整扫描)"
        ])
        self.scan_mode_combo.setCurrentIndex(0)  # 默认智能模式
        self.scan_mode_combo.setToolTip(
            "智能模式: 适中扫描，过滤重复变量\n"
            "摘要模式: 仅扫描DB基本信息\n"
            "详细模式: 完整扫描所有可能变量"
        )
        mode_layout.addWidget(self.scan_mode_combo)
        discover_layout.addLayout(mode_layout)

        # 发现按钮
        self.discover_btn = QPushButton("发现PLC结构")
        self.discover_btn.setEnabled(False)
        self.discover_btn.setStyleSheet("QPushButton { background-color: #2196F3; color: white; font-weight: bold; }")
        discover_layout.addWidget(self.discover_btn)

        # 按钮布局
        button_layout = QHBoxLayout()

        # 导出按钮
        self.export_btn = QPushButton("导出配置")
        self.export_btn.setEnabled(False)
        button_layout.addWidget(self.export_btn)

        # 查看原始数据按钮
        self.view_raw_btn = QPushButton("查看原始数据")
        self.view_raw_btn.setEnabled(False)
        self.view_raw_btn.setStyleSheet("QPushButton { background-color: #FF9800; color: white; font-weight: bold; }")
        button_layout.addWidget(self.view_raw_btn)

        discover_layout.addLayout(button_layout)

        layout.addWidget(discover_group)
        
        # 日志显示
        log_group = QGroupBox("日志")
        log_layout = QVBoxLayout(log_group)
        
        self.log_text = QTextEdit()
        self.log_text.setMaximumHeight(200)
        self.log_text.setFont(QFont("Consolas", 9))
        log_layout.addWidget(self.log_text)
        
        # 清除日志按钮
        clear_log_btn = QPushButton("清除日志")
        log_layout.addWidget(clear_log_btn)
        clear_log_btn.clicked.connect(self.log_text.clear)
        
        layout.addWidget(log_group)
        
        # 添加弹性空间
        layout.addStretch()
        
        return panel
    
    def create_data_panel(self) -> QWidget:
        """创建数据显示面板"""
        panel = QWidget()
        layout = QVBoxLayout(panel)
        
        # 创建标签页
        tab_widget = QTabWidget()
        layout.addWidget(tab_widget)
        
        # 实时数据标签页
        realtime_tab = QWidget()
        realtime_layout = QVBoxLayout(realtime_tab)
        
        # 数据表格
        self.data_table = QTableWidget()
        self.data_table.setColumnCount(4)
        self.data_table.setHorizontalHeaderLabels(["变量名", "当前值", "数据类型", "描述"])
        self.data_table.horizontalHeader().setStretchLastSection(True)
        realtime_layout.addWidget(self.data_table)
        
        tab_widget.addTab(realtime_tab, "实时数据")
        
        # 变量配置标签页
        config_tab = QWidget()
        config_layout = QVBoxLayout(config_tab)
        
        # 配置表格
        self.config_table = QTableWidget()
        self.config_table.setColumnCount(6)
        self.config_table.setHorizontalHeaderLabels(["变量名", "DB", "偏移", "类型", "位", "描述"])
        config_layout.addWidget(self.config_table)
        
        tab_widget.addTab(config_tab, "变量配置")

        # PLC结构发现标签页
        discover_tab = QWidget()
        discover_layout = QVBoxLayout(discover_tab)

        # 发现结果表格
        self.discover_table = QTableWidget()
        self.discover_table.setColumnCount(7)
        self.discover_table.setHorizontalHeaderLabels(["变量名", "DB", "偏移", "类型", "位", "当前值", "描述"])
        self.discover_table.horizontalHeader().setStretchLastSection(True)
        discover_layout.addWidget(self.discover_table)

        # 发现摘要
        self.discover_summary = QTextEdit()
        self.discover_summary.setMaximumHeight(100)
        self.discover_summary.setFont(QFont("Consolas", 9))
        self.discover_summary.setReadOnly(True)
        discover_layout.addWidget(self.discover_summary)

        tab_widget.addTab(discover_tab, "PLC结构发现")

        return panel

    def setup_status_bar(self):
        """设置状态栏"""
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)

        # 连接状态标签
        self.conn_status_label = QLabel("未连接")
        self.conn_status_label.setStyleSheet("color: red; font-weight: bold;")
        self.status_bar.addWidget(self.conn_status_label)

        # 分隔符
        self.status_bar.addWidget(QLabel(" | "))

        # 监控状态标签
        self.monitor_status_label = QLabel("监控停止")
        self.status_bar.addWidget(self.monitor_status_label)

        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        self.status_bar.addPermanentWidget(self.progress_bar)

    def setup_connections(self):
        """设置信号连接"""
        # 按钮连接
        self.connect_btn.clicked.connect(self.toggle_connection)
        self.monitor_btn.clicked.connect(self.toggle_monitoring)
        self.write_btn.clicked.connect(self.write_variable)
        self.discover_btn.clicked.connect(self.discover_plc_structure)
        self.export_btn.clicked.connect(self.export_discovered_config)
        self.view_raw_btn.clicked.connect(self.view_raw_data)

        # S7客户端信号连接
        self.s7_client.connection_changed.connect(self.on_connection_changed)
        self.s7_client.data_received.connect(self.on_data_received)
        self.s7_client.error_occurred.connect(self.on_error_occurred)

    def setup_timer(self):
        """设置定时器"""
        self.update_timer = QTimer()
        self.update_timer.timeout.connect(self.update_ui)
        self.update_timer.start(100)  # 100ms更新一次UI

    def load_config(self):
        """加载配置"""
        plc_config = app_config.get_plc_config()

        # 加载PLC连接配置
        self.ip_edit.setText(plc_config.get("ip", "************"))
        self.rack_spin.setValue(plc_config.get("rack", 0))
        self.slot_spin.setValue(plc_config.get("slot", 1))

        # 加载变量配置
        self.load_variables()

        # 加载UI配置
        ui_config = app_config.get_ui_config()
        interval = ui_config.get("refresh_interval", 1000) / 1000.0
        self.interval_spin.setValue(interval)

    def load_variables(self):
        """加载变量配置"""
        variables = app_config.get_all_variables()

        # 更新变量下拉框
        self.var_combo.clear()
        self.var_combo.addItems(list(variables.keys()))

        # 更新数据表格
        self.data_table.setRowCount(len(variables))
        for row, (var_name, var_config) in enumerate(variables.items()):
            self.data_table.setItem(row, 0, QTableWidgetItem(var_name))
            self.data_table.setItem(row, 1, QTableWidgetItem("--"))
            self.data_table.setItem(row, 2, QTableWidgetItem(var_config.get("type", "")))
            self.data_table.setItem(row, 3, QTableWidgetItem(var_config.get("description", "")))

        # 更新配置表格
        self.config_table.setRowCount(len(variables))
        for row, (var_name, var_config) in enumerate(variables.items()):
            self.config_table.setItem(row, 0, QTableWidgetItem(var_name))
            self.config_table.setItem(row, 1, QTableWidgetItem(str(var_config.get("db", ""))))
            self.config_table.setItem(row, 2, QTableWidgetItem(str(var_config.get("offset", ""))))
            self.config_table.setItem(row, 3, QTableWidgetItem(var_config.get("type", "")))
            self.config_table.setItem(row, 4, QTableWidgetItem(str(var_config.get("bit", ""))))
            self.config_table.setItem(row, 5, QTableWidgetItem(var_config.get("description", "")))

    @Slot()
    def toggle_connection(self):
        """切换连接状态"""
        if self.s7_client.is_connected():
            self.disconnect_plc()
        else:
            self.connect_plc()

    def connect_plc(self):
        """连接PLC"""
        ip = self.ip_edit.text().strip()
        rack = self.rack_spin.value()
        slot = self.slot_spin.value()

        if not ip:
            QMessageBox.warning(self, "警告", "请输入PLC IP地址")
            return

        self.log_message(f"正在连接PLC: {ip}:{rack}:{slot}")
        self.progress_bar.setVisible(True)

        # 在后台线程中连接
        success = self.s7_client.connect_plc(ip, rack, slot)

        self.progress_bar.setVisible(False)

        if success:
            self.log_message("PLC连接成功")
        else:
            self.log_message("PLC连接失败")

    def disconnect_plc(self):
        """断开PLC连接"""
        self.s7_client.disconnect_plc()
        self.log_message("PLC连接已断开")

    @Slot()
    def toggle_monitoring(self):
        """切换监控状态"""
        if self.monitoring:
            self.stop_monitoring()
        else:
            self.start_monitoring()

    def start_monitoring(self):
        """开始监控"""
        if not self.s7_client.is_connected():
            QMessageBox.warning(self, "警告", "请先连接PLC")
            return

        interval = self.interval_spin.value()

        # 检查是否有发现的变量可以监控
        if hasattr(self, 'discovery_result') and self.discovery_result:
            # 启动发现变量的实时监控
            self.start_discovery_monitoring(interval)
        else:
            # 启动常规变量监控
            self.s7_client.start_monitoring(interval)

        self.monitoring = True
        self.monitor_btn.setText("停止监控")
        self.monitor_status_label.setText("监控运行中")
        self.log_message(f"开始监控，间隔: {interval}秒")

    def stop_monitoring(self):
        """停止监控"""
        self.s7_client.stop_monitoring()

        # 停止发现变量监控
        if hasattr(self, 'discovery_monitor_thread') and self.discovery_monitor_thread:
            self.discovery_monitor_thread.stop()
            self.discovery_monitor_thread = None

        self.monitoring = False
        self.monitor_btn.setText("开始监控")
        self.monitor_status_label.setText("监控停止")
        self.log_message("停止监控")

    def start_discovery_monitoring(self, interval: float):
        """开始监控发现的变量"""
        if hasattr(self, 'discovery_monitor_thread') and self.discovery_monitor_thread:
            self.discovery_monitor_thread.stop()

        # 创建发现变量监控线程
        self.discovery_monitor_thread = DiscoveryMonitorThread(
            self.s7_client, self.discovery_result, interval
        )
        self.discovery_monitor_thread.variable_updated.connect(self.update_discovery_table_value)
        self.discovery_monitor_thread.start()

        self.log_message("开始监控发现的变量")

    @Slot(int, str, object)
    def update_discovery_table_value(self, row: int, var_name: str, value):
        """更新发现结果表中的变量值"""
        try:
            if row < self.discover_table.rowCount():
                # 更新当前值列（第5列）
                value_str = str(value) if value is not None else "读取失败"
                self.discover_table.setItem(row, 5, QTableWidgetItem(value_str))

                # 如果值发生变化，高亮显示
                item = self.discover_table.item(row, 5)
                if item:
                    item.setBackground(Qt.GlobalColor.yellow)
                    # 1秒后恢复正常颜色
                    QTimer.singleShot(1000, lambda: self.reset_table_item_color(row, 5))

        except Exception as e:
            self.log_message(f"更新表格值失败: {e}")

    def reset_table_item_color(self, row: int, col: int):
        """重置表格项颜色"""
        try:
            if row < self.discover_table.rowCount():
                item = self.discover_table.item(row, col)
                if item:
                    item.setBackground(Qt.GlobalColor.white)
        except:
            pass

    @Slot()
    def write_variable(self):
        """写入变量"""
        if not self.s7_client.is_connected():
            QMessageBox.warning(self, "警告", "请先连接PLC")
            return

        var_name = self.var_combo.currentText()
        value_text = self.value_edit.text().strip()

        if not var_name or not value_text:
            QMessageBox.warning(self, "警告", "请选择变量并输入值")
            return

        # 根据变量类型转换值
        var_config = app_config.get_variable_config(var_name)
        var_type = var_config.get("type", "")

        try:
            if var_type == "BOOL":
                value = value_text.lower() in ("true", "1", "on", "yes")
            elif var_type in ("INT", "DINT"):
                value = int(value_text)
            elif var_type == "REAL":
                value = float(value_text)
            elif var_type == "BYTE":
                value = int(value_text) & 0xFF
            else:
                value = value_text

            success = self.s7_client.write_variable(var_name, value)
            if success:
                self.log_message(f"写入成功: {var_name} = {value}")
                self.value_edit.clear()
            else:
                self.log_message(f"写入失败: {var_name}")

        except ValueError:
            QMessageBox.warning(self, "错误", f"无效的{var_type}类型值: {value_text}")

    @Slot()
    def discover_plc_structure(self):
        """发现PLC结构"""
        if not self.s7_client.is_connected():
            QMessageBox.warning(self, "警告", "请先连接PLC")
            return

        start_db = self.start_db_spin.value()
        end_db = self.end_db_spin.value()

        if start_db > end_db:
            QMessageBox.warning(self, "错误", "起始DB号不能大于结束DB号")
            return

        # 获取扫描模式
        mode_text = self.scan_mode_combo.currentText()
        if "智能模式" in mode_text:
            scan_mode = "smart"
        elif "摘要模式" in mode_text:
            scan_mode = "summary"
        else:
            scan_mode = "detailed"

        # 显示进度
        self.progress_bar.setVisible(True)
        self.discover_btn.setEnabled(False)
        self.log_message(f"开始发现PLC结构 (DB{start_db}-DB{end_db}, {mode_text})...")

        try:
            # 创建并启动发现线程
            self.discovery_thread = DiscoveryThread(self.s7_client, start_db, end_db, scan_mode)
            self.discovery_thread.discovery_completed.connect(self.on_discovery_completed)
            self.discovery_thread.discovery_failed.connect(self.on_discovery_failed)
            self.discovery_thread.start()

        except Exception as e:
            self.progress_bar.setVisible(False)
            self.discover_btn.setEnabled(True)
            self.log_message(f"发现失败: {e}")

    @Slot(dict)
    def on_discovery_completed(self, result: Dict):
        """发现完成处理"""
        try:
            # 保存发现结果
            self.discovery_result = result
            self.progress_bar.setVisible(False)
            self.discover_btn.setEnabled(True)

            # 存储发现结果
            self.discovery_result = result

            # 更新发现结果表格
            variables = result.get("variables", [])
            self.discover_table.setRowCount(len(variables))

            for row, var in enumerate(variables):
                self.discover_table.setItem(row, 0, QTableWidgetItem(var.get("name", "")))
                self.discover_table.setItem(row, 1, QTableWidgetItem(str(var.get("db", ""))))
                self.discover_table.setItem(row, 2, QTableWidgetItem(str(var.get("offset", ""))))
                self.discover_table.setItem(row, 3, QTableWidgetItem(var.get("type", "")))
                self.discover_table.setItem(row, 4, QTableWidgetItem(str(var.get("bit", ""))))
                self.discover_table.setItem(row, 5, QTableWidgetItem(str(var.get("value", ""))))
                self.discover_table.setItem(row, 6, QTableWidgetItem(var.get("description", "")))

            # 更新摘要信息
            summary = result.get("summary", {})
            summary_text = f"""发现摘要:
• 扫描数据块: {summary.get('total_dbs_scanned', 0)}个
• 可访问数据块: {summary.get('accessible_dbs', 0)}个 {summary.get('accessible_db_numbers', [])}
• 总数据大小: {summary.get('total_data_size', 0)}字节
• 发现变量: {summary.get('total_variables_found', 0)}个
• 变量类型分布: {summary.get('variable_types', {})}"""

            self.discover_summary.setText(summary_text)

            # 启用导出按钮和查看原始数据按钮
            self.export_btn.setEnabled(True)
            self.view_raw_btn.setEnabled(True)

            self.log_message(f"发现完成: {summary.get('accessible_dbs', 0)}个数据块, {summary.get('total_variables_found', 0)}个变量")

        except Exception as e:
            self.log_message(f"处理发现结果失败: {e}")

    @Slot(str)
    def on_discovery_failed(self, error_msg: str):
        """发现失败处理"""
        self.progress_bar.setVisible(False)
        self.discover_btn.setEnabled(True)
        self.log_message(f"发现失败: {error_msg}")
        QMessageBox.critical(self, "错误", f"PLC结构发现失败:\n{error_msg}")

    @Slot()
    def export_discovered_config(self):
        """导出发现的配置"""
        if not hasattr(self, 'discovery_result') or not self.discovery_result:
            QMessageBox.warning(self, "警告", "没有可导出的发现结果")
            return

        try:
            from PySide6.QtWidgets import QFileDialog
            import json

            # 选择保存文件
            file_path, _ = QFileDialog.getSaveFileName(
                self, "导出配置", "discovered_config.json", "JSON文件 (*.json)"
            )

            if not file_path:
                return

            # 转换发现结果为配置格式
            variables = self.discovery_result.get("variables", [])
            config_variables = {}

            for var in variables:
                var_name = var.get("name", "")
                if var_name:
                    config_var = {
                        "db": var.get("db"),
                        "offset": var.get("offset"),
                        "type": var.get("type"),
                        "description": var.get("description", "")
                    }

                    # 如果是BOOL类型，添加bit信息
                    if var.get("type") == "BOOL" and "bit" in var:
                        config_var["bit"] = var["bit"]

                    # 如果是STRING类型，添加长度信息
                    if var.get("type") == "STRING" and "length" in var:
                        config_var["length"] = var["length"]

                    config_variables[var_name] = config_var

            # 创建完整配置
            export_config = {
                "plc": app_config.get_plc_config(),
                "variables": config_variables,
                "discovery_info": {
                    "discovery_time": time.time(),
                    "summary": self.discovery_result.get("summary", {}),
                    "data_blocks": {
                        str(k): {
                            "size": v.get("size", 0),
                            "accessible": v.get("accessible", False)
                        }
                        for k, v in self.discovery_result.get("data_blocks", {}).items()
                        if v.get("accessible", False)
                    }
                }
            }

            # 保存文件
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(export_config, f, indent=4, ensure_ascii=False)

            self.log_message(f"配置已导出到: {file_path}")
            QMessageBox.information(self, "成功", f"配置已成功导出到:\n{file_path}")

        except Exception as e:
            error_msg = f"导出配置失败: {e}"
            self.log_message(error_msg)
            QMessageBox.critical(self, "错误", error_msg)

    @Slot(bool)
    def on_connection_changed(self, connected: bool):
        """连接状态变化处理"""
        if connected:
            self.conn_status_label.setText("已连接")
            self.conn_status_label.setStyleSheet("color: green; font-weight: bold;")
            self.connect_btn.setText("断开")
            self.connect_btn.setStyleSheet("QPushButton { background-color: #f44336; color: white; font-weight: bold; }")
            self.monitor_btn.setEnabled(True)
            self.write_btn.setEnabled(True)
            self.discover_btn.setEnabled(True)
        else:
            self.conn_status_label.setText("未连接")
            self.conn_status_label.setStyleSheet("color: red; font-weight: bold;")
            self.connect_btn.setText("连接")
            self.connect_btn.setStyleSheet("QPushButton { background-color: #4CAF50; color: white; font-weight: bold; }")
            self.monitor_btn.setEnabled(False)
            self.write_btn.setEnabled(False)
            self.discover_btn.setEnabled(False)
            self.export_btn.setEnabled(False)

            # 如果正在监控，停止监控
            if self.monitoring:
                self.stop_monitoring()

    @Slot(str, object)
    def on_data_received(self, var_name: str, value: Any):
        """数据接收处理"""
        # 更新数据表格
        for row in range(self.data_table.rowCount()):
            item = self.data_table.item(row, 0)
            if item and item.text() == var_name:
                value_item = self.data_table.item(row, 1)
                if value_item:
                    value_item.setText(str(value))
                break

    @Slot(str)
    def on_error_occurred(self, error_msg: str):
        """错误处理"""
        self.log_message(f"错误: {error_msg}")

    def log_message(self, message: str):
        """记录日志消息"""
        from datetime import datetime
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {message}"
        self.log_text.append(log_entry)

    @Slot()
    def view_raw_data(self):
        """查看原始数据"""
        if not hasattr(self, 'discovery_result') or not self.discovery_result:
            QMessageBox.warning(self, "警告", "请先执行PLC结构发现")
            return

        # 创建并显示原始数据查看器
        dialog = RawDataViewer(self.discovery_result, self)
        dialog.exec()

        # 自动滚动到底部
        cursor = self.log_text.textCursor()
        cursor.movePosition(QTextCursor.MoveOperation.End)
        self.log_text.setTextCursor(cursor)

    def update_ui(self):
        """更新UI状态"""
        # 这里可以添加定期更新的UI逻辑
        pass

    def closeEvent(self, event):
        """窗口关闭事件"""
        if self.s7_client.is_connected():
            reply = QMessageBox.question(
                self, "确认", "PLC仍然连接中，确定要退出吗？",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
                QMessageBox.StandardButton.No
            )

            if reply == QMessageBox.StandardButton.Yes:
                self.s7_client.disconnect_plc()
                event.accept()
            else:
                event.ignore()
        else:
            event.accept()


class RawDataViewer(QDialog):
    """原始数据查看器"""

    def __init__(self, discovery_result: Dict, parent=None):
        super().__init__(parent)
        self.discovery_result = discovery_result
        self.setup_ui()

    def setup_ui(self):
        """设置UI"""
        self.setWindowTitle("PLC原始数据查看器")
        self.setModal(True)
        self.resize(1000, 700)

        layout = QVBoxLayout(self)

        # 顶部信息
        info_group = QGroupBox("扫描信息")
        info_layout = QVBoxLayout(info_group)

        summary = self.discovery_result.get("summary", {})
        info_text = f"""
扫描模式: {summary.get('scan_mode', 'unknown')}
扫描DB数量: {summary.get('total_dbs_scanned', 0)}
可访问DB数量: {summary.get('accessible_dbs', 0)}
可访问DB编号: {summary.get('accessible_db_numbers', [])}
总数据大小: {summary.get('total_data_size', 0)} 字节
发现变量数量: {summary.get('total_variables_found', 0)}
        """.strip()

        info_label = QLabel(info_text)
        # 使用系统主题色作为背景
        info_label.setStyleSheet("""
            QLabel {
                background-color: palette(window);
                border: 1px solid palette(mid);
                padding: 10px;
                border-radius: 5px;
                color: palette(window-text);
            }
        """)
        info_layout.addWidget(info_label)
        layout.addWidget(info_group)

        # 数据块选择
        db_group = QGroupBox("选择数据块")
        db_layout = QHBoxLayout(db_group)

        db_layout.addWidget(QLabel("数据块:"))
        self.db_combo = QComboBox()

        # 填充数据块列表
        data_blocks = self.discovery_result.get("data_blocks", {})
        accessible_dbs = []
        for db_num, db_info in data_blocks.items():
            if db_info.get("accessible", False):
                accessible_dbs.append(db_num)
                self.db_combo.addItem(f"DB{db_num} ({db_info.get('size', 0)}字节)", db_num)

        if not accessible_dbs:
            self.db_combo.addItem("无可访问的数据块", None)

        self.db_combo.currentIndexChanged.connect(self.on_db_changed)
        db_layout.addWidget(self.db_combo)

        # 显示格式选择
        db_layout.addWidget(QLabel("显示格式:"))
        self.format_combo = QComboBox()
        self.format_combo.addItems(["十六进制 (Hex)", "十进制 (Dec)", "二进制 (Bin)", "ASCII"])
        self.format_combo.currentIndexChanged.connect(self.update_display)
        db_layout.addWidget(self.format_combo)

        # 刷新按钮
        refresh_btn = QPushButton("刷新数据")
        refresh_btn.clicked.connect(self.refresh_data)
        db_layout.addWidget(refresh_btn)

        layout.addWidget(db_group)

        # 数据显示区域
        display_group = QGroupBox("原始数据")
        display_layout = QVBoxLayout(display_group)

        # 创建滚动区域
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)

        # 数据显示文本框
        self.data_text = QTextEdit()
        self.data_text.setFont(QFont("Consolas", 10))
        self.data_text.setReadOnly(True)
        self.data_text.setStyleSheet("""
            QTextEdit {
                background-color: #1e1e1e;
                color: #ffffff;
                border: 1px solid #555;
                padding: 10px;
            }
        """)

        scroll_area.setWidget(self.data_text)
        display_layout.addWidget(scroll_area)
        layout.addWidget(display_group)

        # 底部按钮
        button_layout = QHBoxLayout()

        export_btn = QPushButton("导出原始数据")
        export_btn.clicked.connect(self.export_raw_data)
        button_layout.addWidget(export_btn)

        button_layout.addStretch()

        close_btn = QPushButton("关闭")
        close_btn.clicked.connect(self.close)
        button_layout.addWidget(close_btn)

        layout.addLayout(button_layout)

        # 初始显示
        if accessible_dbs:
            self.update_display()

    def on_db_changed(self):
        """数据块选择改变"""
        self.update_display()

    def update_display(self):
        """更新数据显示"""
        db_num = self.db_combo.currentData()
        if db_num is None:
            self.data_text.setText("无可访问的数据块")
            return

        data_blocks = self.discovery_result.get("data_blocks", {})
        db_info = data_blocks.get(db_num, {})
        raw_data = db_info.get("data", bytearray())

        if not raw_data:
            self.data_text.setText(f"DB{db_num} 无数据")
            return

        format_type = self.format_combo.currentText()

        if "十六进制" in format_type:
            display_text = self.format_hex(raw_data, db_num)
        elif "十进制" in format_type:
            display_text = self.format_decimal(raw_data, db_num)
        elif "二进制" in format_type:
            display_text = self.format_binary(raw_data, db_num)
        else:  # ASCII
            display_text = self.format_ascii(raw_data, db_num)

        self.data_text.setText(display_text)

    def format_hex(self, data: bytearray, db_num: int) -> str:
        """格式化为十六进制显示"""
        lines = []
        lines.append(f"DB{db_num} 原始数据 (十六进制格式)")
        lines.append("=" * 80)
        lines.append("偏移    00 01 02 03 04 05 06 07 08 09 0A 0B 0C 0D 0E 0F    ASCII")
        lines.append("-" * 80)

        for i in range(0, len(data), 16):
            # 偏移地址
            offset = f"{i:04X}    "

            # 十六进制字节
            hex_bytes = []
            ascii_chars = []

            for j in range(16):
                if i + j < len(data):
                    byte_val = data[i + j]
                    hex_bytes.append(f"{byte_val:02X}")
                    # ASCII字符（可打印字符显示，否则显示点）
                    if 32 <= byte_val <= 126:
                        ascii_chars.append(chr(byte_val))
                    else:
                        ascii_chars.append(".")
                else:
                    hex_bytes.append("  ")
                    ascii_chars.append(" ")

            hex_part = " ".join(hex_bytes)
            ascii_part = "".join(ascii_chars)

            lines.append(f"{offset}{hex_part}    {ascii_part}")

        lines.append("-" * 80)
        lines.append(f"总字节数: {len(data)}")

        return "\n".join(lines)

    def format_decimal(self, data: bytearray, db_num: int) -> str:
        """格式化为十进制显示"""
        lines = []
        lines.append(f"DB{db_num} 原始数据 (十进制格式)")
        lines.append("=" * 60)
        lines.append("偏移    字节值 (十进制)")
        lines.append("-" * 60)

        for i, byte_val in enumerate(data):
            lines.append(f"{i:04d}    {byte_val:3d} (0x{byte_val:02X})")

        lines.append("-" * 60)
        lines.append(f"总字节数: {len(data)}")

        return "\n".join(lines)

    def format_binary(self, data: bytearray, db_num: int) -> str:
        """格式化为二进制显示"""
        lines = []
        lines.append(f"DB{db_num} 原始数据 (二进制格式)")
        lines.append("=" * 70)
        lines.append("偏移    二进制值        十六进制  十进制")
        lines.append("-" * 70)

        for i, byte_val in enumerate(data):
            binary = f"{byte_val:08b}"
            lines.append(f"{i:04d}    {binary}    0x{byte_val:02X}     {byte_val:3d}")

        lines.append("-" * 70)
        lines.append(f"总字节数: {len(data)}")

        return "\n".join(lines)

    def format_ascii(self, data: bytearray, db_num: int) -> str:
        """格式化为ASCII显示"""
        lines = []
        lines.append(f"DB{db_num} 原始数据 (ASCII格式)")
        lines.append("=" * 60)
        lines.append("偏移    ASCII字符    十六进制  十进制")
        lines.append("-" * 60)

        for i, byte_val in enumerate(data):
            if 32 <= byte_val <= 126:
                ascii_char = f"'{chr(byte_val)}'"
            else:
                ascii_char = "'.'"

            lines.append(f"{i:04d}    {ascii_char:10}    0x{byte_val:02X}     {byte_val:3d}")

        lines.append("-" * 60)
        lines.append(f"总字节数: {len(data)}")

        return "\n".join(lines)

    def refresh_data(self):
        """刷新数据（重新读取PLC数据）"""
        # 这里可以添加重新读取PLC数据的逻辑
        QMessageBox.information(self, "提示", "数据刷新功能需要重新执行PLC结构发现")

    def export_raw_data(self):
        """导出原始数据"""
        db_num = self.db_combo.currentData()
        if db_num is None:
            QMessageBox.warning(self, "警告", "无可导出的数据")
            return

        file_path, _ = QFileDialog.getSaveFileName(
            self, "导出原始数据", f"DB{db_num}_raw_data.txt", "文本文件 (*.txt);;所有文件 (*)"
        )

        if file_path:
            try:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(self.data_text.toPlainText())
                QMessageBox.information(self, "成功", f"原始数据已导出到:\n{file_path}")
            except Exception as e:
                QMessageBox.critical(self, "错误", f"导出失败:\n{e}")


class DiscoveryMonitorThread(QThread):
    """发现变量监控线程"""
    variable_updated = Signal(int, str, object)  # row, var_name, value

    def __init__(self, s7_client, discovery_result: Dict, interval: float = 1.0):
        super().__init__()
        self.s7_client = s7_client
        self.discovery_result = discovery_result
        self.interval = interval
        self.running = False

    def run(self):
        """运行监控循环"""
        self.running = True
        variables = self.discovery_result.get("variables", [])

        while self.running and self.s7_client.is_connected():
            try:
                for row, var in enumerate(variables):
                    if not self.running:
                        break

                    # 读取变量值
                    value = self.read_variable_value(var)

                    # 发射更新信号
                    self.variable_updated.emit(row, var.get("name", ""), value)

                # 等待指定间隔
                self.msleep(int(self.interval * 1000))

            except Exception as e:
                print(f"监控线程错误: {e}")
                break

    def read_variable_value(self, var: Dict):
        """读取变量值"""
        try:
            db_number = var.get("db", 0)
            offset = var.get("offset", 0)
            var_type = var.get("type", "")
            bit = var.get("bit")

            if var_type == "BOOL" and bit is not None:
                # 读取布尔值
                return self.s7_client.read_bool(db_number, offset, bit)
            elif var_type == "BYTE":
                # 读取字节值
                return self.s7_client.read_byte(db_number, offset)
            elif var_type == "INT":
                # 读取整数值
                return self.s7_client.read_int(db_number, offset)
            elif var_type == "DINT":
                # 读取双整数值
                return self.s7_client.read_dint(db_number, offset)
            elif var_type == "REAL":
                # 读取实数值
                return self.s7_client.read_real(db_number, offset)
            else:
                # 未知类型，尝试读取原始字节
                data = self.s7_client.read_area(db_number, offset, 1)
                return data[0] if data else None

        except Exception as e:
            print(f"读取变量 {var.get('name', '')} 失败: {e}")
            return None

    def stop(self):
        """停止监控"""
        self.running = False
        self.wait(2000)  # 等待最多2秒


